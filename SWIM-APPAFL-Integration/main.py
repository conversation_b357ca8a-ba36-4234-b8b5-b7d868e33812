# SWIM-APPAFL集成主程序
# 支持两种训练算法的选择：1.APPAFL标准训练 2.SWIM对比学习训练
import argparse
import os
import torch
import numpy as np
import random
import copy
from models import CNNCifar, CNNCifarWithProjection, ResNetCifarWithProjection, CNNCifarSWIMBackbone, ResNetCifarSWIMBackbone, ResNet50CifarWithProjection, ResNet50CifarSWIMBackbone
from swim_models import ModelFedCon_SWIM, ModelFedCon_SWIM_NoHeader
from clients import ClientsGroup
from server import APPAFLServer


def set_seed(seed):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def get_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='SWIM-APPAFL集成联邦学习')
    
    # 基本参数
    parser.add_argument('--seed', type=int, default=0, help='随机种子')
    parser.add_argument('--device', type=str, default='cuda', help='计算设备')
    parser.add_argument('--datadir', type=str, default='./data', help='数据目录')
    parser.add_argument('--logdir', type=str, default='./logs', help='日志目录')
    
    # 数据集参数
    parser.add_argument('--dataset', type=str, default='cifar10', choices=['cifar10', 'cifar100'],
                        help='数据集选择')
    parser.add_argument('--partition', type=str, default='noniid', choices=['iid', 'noniid'], 
                        help='数据分布类型')
    parser.add_argument('--beta', type=float, default=0.2, help='非IID数据分布参数')
    
    # 联邦学习参数
    parser.add_argument('--n_clients', type=int, default=50, help='客户端数量')
    parser.add_argument('--cfraction', type=float, default=0.2, help='每轮参与的客户端比例')
    parser.add_argument('--local_epochs', type=int, default=10, help='本地训练轮数')
    parser.add_argument('--comm_rounds', type=int, default=100, help='通信轮数')
    parser.add_argument('--lr', type=float, default=0.01, help='学习率')

    # 数据加载参数
    parser.add_argument('--train_batch_size', type=int, default=128, help='训练批次大小')
    parser.add_argument('--test_batch_size', type=int, default=128, help='测试批次大小')
    
    # 模型参数
    parser.add_argument('--model', type=str, default='resnet', choices=['cnn', 'resnet', 'simple-cnn'],
                        help='模型类型')
    
    # SWIM算法参数
    parser.add_argument('--use_swim', type=int, default=1, choices=[0, 1],
                        help='是否使用SWIM算法 (0: 不使用, 1: 使用)')
    parser.add_argument('--out_dim', type=int, default=256, help='投影头输出维度')
    #parser.add_argument('--temperature', type=float, default=0.5, help='对比学习温度参数')
    parser.add_argument('--temperature', type=float, default=0.5, help='对比学习温度参数')
    parser.add_argument('--model_buffer_size', type=int, default=1, help='历史模型缓冲区大小')
    #parser.add_argument('--model_buffer_size', type=int, default=2, help='历史模型缓冲区大小')
    parser.add_argument('--kr', type=float, default=0.4, help='滑动窗口比例参数')
    #parser.add_argument('--kr', type=float, default=0.3, help='滑动窗口比例参数')
    parser.add_argument('--async_weight_strategy', type=str, default='local_rounds',
                        choices=['local_rounds', 'global_progress', 'fixed_schedule', 'original'],
                        help='异步环境下的SWIM动态权重计算策略')

    return parser.parse_args()


def create_model(args, verbose=True):
    """创建模型（使用原始SWIM的模型架构）"""
    n_classes = 10 if args.dataset == 'cifar10' else 100

    # 根据模型类型和数据集选择基础模型
    if args.model == 'simple-cnn':
        base_model = "simple-cnn"
    elif args.model == 'resnet':
        # 根据数据集选择ResNet类型
        if args.dataset == 'cifar100':
            base_model = "resnet18-cifar100"  # 与原始SWIM一致
        else:
            base_model = "resnet18"  # CIFAR-10使用标准ResNet18
    else:
        # 默认使用ResNet
        if args.dataset == 'cifar100':
            base_model = "resnet18-cifar100"
        else:
            base_model = "resnet18"

    if args.use_swim == 1:
        # 使用SWIM算法，创建带投影头的模型（与原始SWIM完全一致）
        model = ModelFedCon_SWIM(
            base_model=base_model,
            out_dim=args.out_dim,
            n_classes=n_classes
        )
        if verbose:
            print(f"✓ 创建原始SWIM模型（带投影头）: {model.__class__.__name__}, 基础模型: {base_model}")
    else:
        # 使用APPAFL标准训练，创建与SWIM相同架构但无投影头的模型
        model = ModelFedCon_SWIM_NoHeader(
            base_model=base_model,
            out_dim=args.out_dim,
            n_classes=n_classes
        )
        if verbose:
            print(f"✓ 创建原始SWIM模型（无投影头）: {model.__class__.__name__}, 基础模型: {base_model}")

    return model


def main():
    """主函数"""
    args = get_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建目录
    os.makedirs(args.datadir, exist_ok=True)
    os.makedirs(args.logdir, exist_ok=True)
    
    # 打印配置信息
    print("\n=== SWIM-APPAFL集成联邦学习配置 ===")
    print(f"数据集: {args.dataset}")
    print(f"数据分布: {args.partition} (beta={args.beta})")
    print(f"客户端数量: {args.n_clients}")
    print(f"每轮参与比例: {args.cfraction}")
    print(f"本地训练轮数: {args.local_epochs}")
    print(f"通信轮数: {args.comm_rounds}")
    print(f"学习率: {args.lr}")
    print(f"训练批次大小: {args.train_batch_size}")
    print(f"测试批次大小: {args.test_batch_size}")
    print(f"使用SWIM算法: {'是' if args.use_swim == 1 else '否'}")
    if args.use_swim == 1:
        print(f"投影头维度: {args.out_dim}")
        print(f"温度参数: {args.temperature}")
        print(f"滑动窗口参数: {args.kr}")
        print(f"历史模型缓冲区大小: {args.model_buffer_size}")
        print(f"异步权重策略: {args.async_weight_strategy}")

    print(f"批次大小配置:")
    print(f"- 训练批次大小: {args.train_batch_size}")
    print(f"- 测试批次大小: {args.test_batch_size}")
    
    # 创建模型
    model = create_model(args)
    
    # 创建客户端组
    print("\n=== 创建客户端组 ===")
    clients_group = ClientsGroup(
        dataset=args.dataset,
        datadir=args.datadir,
        partition=args.partition,
        n_clients=args.n_clients,
        beta=args.beta,
        device=device,
        train_batch_size=args.train_batch_size,
        test_batch_size=args.test_batch_size
    )
    
    # 创建服务器
    print("\n=== 创建服务器 ===")
    server = APPAFLServer(
        model=model,
        device=device,
        n_clients=args.n_clients,
        cfraction=args.cfraction
    )
    
    # SWIM算法相关变量
    global_model_for_swim = None
    historical_models = []
    
    if args.use_swim == 1:
        # 创建全局模型的副本用于SWIM
        global_model_for_swim = create_model(args)
        global_model_for_swim.load_state_dict(server.global_parameters)
        global_model_for_swim.to('cpu')  # 保持在CPU上节省显存
        print("✓ 初始化SWIM算法相关组件")
    
    # 开始联邦学习训练
    print("\n=== 开始联邦学习训练 ===")
    accuracy_list = []
    
    for round_num in range(args.comm_rounds):
        print(f"\n--- 第 {round_num + 1}/{args.comm_rounds} 轮通信 ---")
        
        # 选择参与训练的客户端（使用原始APPAFL逻辑）
        training_clients, stale_model_clients = server.select_clients(round_num)
        all_clients = training_clients + stale_model_clients

        if not all_clients:
            print("警告：没有客户端参与本轮训练")
            continue

        print(f"参与训练的客户端: {len(training_clients)} 个")
        print(f"使用陈旧模型的客户端: {len(stale_model_clients)} 个")
        print(f"总参与客户端: {len(all_clients)} 个")
        
        # 收集客户端模型（区分训练客户端和使用陈旧模型的客户端）
        client_models = []
        client_ids = []

        # 处理实际参与训练的客户端
        for client_id in training_clients:
            # 获取最新的全局模型参数
            client_params = server.get_model_for_client(client_id, is_stale=False)

            # 客户端本地训练
            client = clients_group.clients[f'client{client_id}']

            if args.use_swim == 1:
                # 使用SWIM算法训练
                updated_params = client.localUpdateWithSWIM(
                    model=copy.deepcopy(model),
                    epochs=args.local_epochs,
                    lr=args.lr,
                    global_parameters=client_params,
                    global_model=global_model_for_swim,
                    previous_models=historical_models,
                    mu=0.5,
                    temperature=args.temperature,
                    round_num=round_num,
                    total_rounds=args.comm_rounds,
                    kr=args.kr,
                    async_weight_strategy=args.async_weight_strategy
                )
            else:
                # 使用APPAFL标准训练（使用与SWIM相同架构但无投影头的模型）
                updated_params = client.localUpdate(
                    model=copy.deepcopy(model),
                    epochs=args.local_epochs,
                    lr=args.lr,
                    global_parameters=client_params,
                    mu=0
                )

            client_models.append(updated_params)
            client_ids.append(client_id)

        # 处理使用陈旧模型的客户端（与原始APPAFL完全一致）
        # 这些客户端使用陈旧的全局模型进行本地训练，然后参与聚合
        for client_id in stale_model_clients:
            # 获取陈旧的全局模型参数
            stale_params = server.get_model_for_client(client_id, is_stale=True)

            # 客户端使用陈旧模型进行本地训练
            client = clients_group.clients[f'client{client_id}']

            if args.use_swim == 1:
                # 使用SWIM算法训练（基于陈旧模型）
                updated_params = client.localUpdateWithSWIM(
                    model=copy.deepcopy(model),
                    epochs=args.local_epochs,
                    lr=args.lr,
                    global_parameters=stale_params,
                    global_model=global_model_for_swim,
                    previous_models=historical_models,
                    mu=0.5,
                    temperature=args.temperature,
                    round_num=round_num,
                    total_rounds=args.comm_rounds,
                    kr=args.kr,
                    async_weight_strategy=args.async_weight_strategy
                )
            else:
                # 使用APPAFL标准训练（基于陈旧模型）
                updated_params = client.localUpdate(
                    model=copy.deepcopy(model),
                    epochs=args.local_epochs,
                    lr=args.lr,
                    global_parameters=stale_params,
                    mu=0
                )

            client_models.append(updated_params)
            client_ids.append(client_id)
            print(f"客户端{client_id}使用陈旧模型训练并参与聚合，权重: {server.client_weights[client_id]:.4f}")
        
        # 服务器聚合
        aggregated_params = server.aggregate_models(client_models, client_ids, round_num)
        server.update_global_model(aggregated_params)
        
        # 更新SWIM相关组件
        if args.use_swim == 1:
            # 保存当前全局模型到历史模型池
            if len(historical_models) >= args.model_buffer_size:
                historical_models.pop(0)  # 移除最旧的模型

            # 创建当前全局模型的副本并添加到历史模型池（不显示创建信息）
            historical_model = create_model(args, verbose=False)
            historical_model.load_state_dict(server.global_parameters)
            historical_models.append(historical_model.to('cpu'))

            # 更新全局模型
            global_model_for_swim.load_state_dict(server.global_parameters)
        
        # 评估模型性能（每轮都评估，但只在特定轮次详细显示）
        accuracy, loss = server.evaluate_model(clients_group.test_dataloader)
        accuracy_list.append(accuracy)

        if (round_num + 1) % 10 == 0 or round_num == args.comm_rounds - 1:
            print(f"第 {round_num + 1} 轮 - 准确率: {accuracy:.2f}%, 损失: {loss:.4f}")
        else:
            # 简化显示
            print(f"准确率: {accuracy:.2f}%")
        
        # 打印客户端状态（每20轮打印一次）
        if (round_num + 1) % 20 == 0:
            server.print_client_status(round_num)
    
    # 最终评估
    print("\n=== 训练完成 ===")
    final_accuracy, final_loss = server.evaluate_model(clients_group.test_dataloader)
    print(f"最终准确率: {final_accuracy:.2f}%")
    print(f"最终损失: {final_loss:.4f}")
    
    # 保存结果
    results = {
        'final_accuracy': final_accuracy,
        'final_loss': final_loss,
        'accuracy_history': accuracy_list,
        'config': vars(args)
    }
    
    result_file = f"new_sgd_results_{args.dataset}_{args.partition}_swim{args.use_swim}_{args.n_clients}_{args.cfraction}.pt"
    torch.save(results, os.path.join(args.logdir, result_file))
    print(f"结果已保存到: {result_file}")


if __name__ == '__main__':
    main()
