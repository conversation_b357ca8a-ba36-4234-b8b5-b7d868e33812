# 测试ResNet50模型的脚本
# 验证ResNet50模型在SWIM-APPAFL集成中是否能正常工作

import torch
import torch.nn as nn
from models import ResNet50CifarWithProjection, ResNet50CifarSWIMBackbone
from swim_models import ModelFedCon_SWIM, ModelFedCon_SWIM_NoHeader


def test_resnet50_models():
    """测试ResNet50相关的所有模型"""
    print("=" * 60)
    print("测试ResNet50模型")
    print("=" * 60)
    
    # 测试参数
    batch_size = 4
    n_classes_cifar10 = 10
    n_classes_cifar100 = 100
    out_dim = 256
    
    # 创建测试数据
    test_data_cifar = torch.randn(batch_size, 3, 32, 32)
    
    print(f"测试数据形状: {test_data_cifar.shape}")
    print()
    
    # 1. 测试models.py中的ResNet50CifarWithProjection
    print("1. 测试 ResNet50CifarWithProjection (CIFAR-10)")
    try:
        model1 = ResNet50CifarWithProjection(out_dim=out_dim, n_classes=n_classes_cifar10)
        features, projection, classification = model1(test_data_cifar)
        print(f"   ✓ 特征形状: {features.shape}")
        print(f"   ✓ 投影形状: {projection.shape}")
        print(f"   ✓ 分类形状: {classification.shape}")
        print(f"   ✓ 模型参数数量: {sum(p.numel() for p in model1.parameters()):,}")
    except Exception as e:
        print(f"   ✗ 错误: {e}")
    print()
    
    # 2. 测试models.py中的ResNet50CifarWithProjection (CIFAR-100)
    print("2. 测试 ResNet50CifarWithProjection (CIFAR-100)")
    try:
        model2 = ResNet50CifarWithProjection(out_dim=out_dim, n_classes=n_classes_cifar100)
        features, projection, classification = model2(test_data_cifar)
        print(f"   ✓ 特征形状: {features.shape}")
        print(f"   ✓ 投影形状: {projection.shape}")
        print(f"   ✓ 分类形状: {classification.shape}")
        print(f"   ✓ 模型参数数量: {sum(p.numel() for p in model2.parameters()):,}")
    except Exception as e:
        print(f"   ✗ 错误: {e}")
    print()
    
    # 3. 测试models.py中的ResNet50CifarSWIMBackbone
    print("3. 测试 ResNet50CifarSWIMBackbone (CIFAR-10)")
    try:
        model3 = ResNet50CifarSWIMBackbone(n_classes=n_classes_cifar10)
        classification = model3(test_data_cifar)
        print(f"   ✓ 分类形状: {classification.shape}")
        print(f"   ✓ 模型参数数量: {sum(p.numel() for p in model3.parameters()):,}")
    except Exception as e:
        print(f"   ✗ 错误: {e}")
    print()
    
    # 4. 测试models.py中的ResNet50CifarSWIMBackbone (CIFAR-100)
    print("4. 测试 ResNet50CifarSWIMBackbone (CIFAR-100)")
    try:
        model4 = ResNet50CifarSWIMBackbone(n_classes=n_classes_cifar100)
        classification = model4(test_data_cifar)
        print(f"   ✓ 分类形状: {classification.shape}")
        print(f"   ✓ 模型参数数量: {sum(p.numel() for p in model4.parameters()):,}")
    except Exception as e:
        print(f"   ✗ 错误: {e}")
    print()
    
    # 5. 测试swim_models.py中的ModelFedCon_SWIM (ResNet50)
    print("5. 测试 ModelFedCon_SWIM (ResNet50, CIFAR-10)")
    try:
        model5 = ModelFedCon_SWIM(
            base_model="resnet50",
            out_dim=out_dim,
            n_classes=n_classes_cifar10
        )
        features, projection, classification = model5(test_data_cifar)
        print(f"   ✓ 特征形状: {features.shape}")
        print(f"   ✓ 投影形状: {projection.shape}")
        print(f"   ✓ 分类形状: {classification.shape}")
        print(f"   ✓ 模型参数数量: {sum(p.numel() for p in model5.parameters()):,}")
    except Exception as e:
        print(f"   ✗ 错误: {e}")
    print()
    
    # 6. 测试swim_models.py中的ModelFedCon_SWIM (ResNet50, CIFAR-100)
    print("6. 测试 ModelFedCon_SWIM (ResNet50, CIFAR-100)")
    try:
        model6 = ModelFedCon_SWIM(
            base_model="resnet50-cifar100",
            out_dim=out_dim,
            n_classes=n_classes_cifar100
        )
        features, projection, classification = model6(test_data_cifar)
        print(f"   ✓ 特征形状: {features.shape}")
        print(f"   ✓ 投影形状: {projection.shape}")
        print(f"   ✓ 分类形状: {classification.shape}")
        print(f"   ✓ 模型参数数量: {sum(p.numel() for p in model6.parameters()):,}")
    except Exception as e:
        print(f"   ✗ 错误: {e}")
    print()
    
    # 7. 测试swim_models.py中的ModelFedCon_SWIM_NoHeader (ResNet50)
    print("7. 测试 ModelFedCon_SWIM_NoHeader (ResNet50, CIFAR-10)")
    try:
        model7 = ModelFedCon_SWIM_NoHeader(
            base_model="resnet50",
            out_dim=out_dim,
            n_classes=n_classes_cifar10
        )
        features, projection, classification = model7(test_data_cifar)
        print(f"   ✓ 特征形状: {features.shape}")
        print(f"   ✓ 投影形状: {projection if projection is not None else 'None'}")
        print(f"   ✓ 分类形状: {classification.shape}")
        print(f"   ✓ 模型参数数量: {sum(p.numel() for p in model7.parameters()):,}")
    except Exception as e:
        print(f"   ✗ 错误: {e}")
    print()
    
    # 8. 测试swim_models.py中的ModelFedCon_SWIM_NoHeader (ResNet50, CIFAR-100)
    print("8. 测试 ModelFedCon_SWIM_NoHeader (ResNet50, CIFAR-100)")
    try:
        model8 = ModelFedCon_SWIM_NoHeader(
            base_model="resnet50-cifar100",
            out_dim=out_dim,
            n_classes=n_classes_cifar100
        )
        features, projection, classification = model8(test_data_cifar)
        print(f"   ✓ 特征形状: {features.shape}")
        print(f"   ✓ 投影形状: {projection if projection is not None else 'None'}")
        print(f"   ✓ 分类形状: {classification.shape}")
        print(f"   ✓ 模型参数数量: {sum(p.numel() for p in model8.parameters()):,}")
    except Exception as e:
        print(f"   ✗ 错误: {e}")
    print()
    
    print("=" * 60)
    print("ResNet50模型测试完成")
    print("=" * 60)


def test_model_compatibility():
    """测试模型兼容性"""
    print("\n测试模型兼容性...")
    
    # 测试ResNet18和ResNet50的特征维度是否一致
    model_resnet18 = ModelFedCon_SWIM(base_model="resnet18", out_dim=256, n_classes=10)
    model_resnet50 = ModelFedCon_SWIM(base_model="resnet50", out_dim=256, n_classes=10)
    
    test_data = torch.randn(2, 3, 32, 32)
    
    features18, proj18, class18 = model_resnet18(test_data)
    features50, proj50, class50 = model_resnet50(test_data)
    
    print(f"ResNet18 特征维度: {features18.shape}")
    print(f"ResNet50 特征维度: {features50.shape}")
    print(f"ResNet18 投影维度: {proj18.shape}")
    print(f"ResNet50 投影维度: {proj50.shape}")
    print(f"ResNet18 分类维度: {class18.shape}")
    print(f"ResNet50 分类维度: {class50.shape}")
    
    # 检查输出维度是否一致
    if proj18.shape == proj50.shape and class18.shape == class50.shape:
        print("✓ ResNet18和ResNet50的输出维度兼容")
    else:
        print("✗ ResNet18和ResNet50的输出维度不兼容")


if __name__ == "__main__":
    test_resnet50_models()
    test_model_compatibility()
